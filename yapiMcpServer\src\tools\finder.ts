import * as fs from 'fs';
import * as path from 'path';

interface StructDefinition {
  name: string;
  content: string;
  dependencies: string[];
  packageName?: string;
  importPath?: string;
}

/**
 * 在项目中查找Go结构体定义
 * @param structName 要查找的结构体名称
 * @param rootDir 项目的根目录
 * @returns 结构体定义及其依赖项
 */
export function findStructDefinitions(rootDir: string, structName: string): string {
  // 存储找到的结构体定义
  const foundStructs: Map<string, StructDefinition> = new Map();
  // 存储需要查找的结构体名称
  const structsToFind: Set<string> = new Set([structName]);
  // 存储已处理的结构体名称
  const processedStructs: Set<string> = new Set();

  // 处理结构体直到没有更多结构体需要查找
  while (structsToFind.size > 0) {
    const currentStructName = Array.from(structsToFind)[0];
    structsToFind.delete(currentStructName);
    
    if (processedStructs.has(currentStructName)) {
      continue;
    }
    
    processedStructs.add(currentStructName);
    
    // 查找结构体定义
    const structDef = findStructInProject(currentStructName, rootDir);
    if (structDef) {
      foundStructs.set(currentStructName, structDef);
      
      // 将依赖项添加到需要查找的结构体列表中
      for (const dep of structDef.dependencies) {
        if (!processedStructs.has(dep) && !foundStructs.has(dep)) {
          structsToFind.add(dep);
        }
      }
    }
  }

  // 现在专门查找未找到的导入类型
  const importedTypes = new Set<string>();
  
  // 从依赖项中收集所有导入的类型
  for (const struct of foundStructs.values()) {
    for (const dep of struct.dependencies) {
      if (dep.includes('.') && !foundStructs.has(dep)) {
        importedTypes.add(dep);
      }
    }
  }
  
  // 查找导入的类型
  for (const importedType of importedTypes) {
    const [packageName, typeName] = importedType.split('.');
    
    // 首先尝试查找包目录
    const packageDir = findPackageDir(packageName, rootDir);
    if (packageDir) {
      const structDef = findStructInFiles(getAllGoFiles(packageDir), typeName);
      if (structDef) {
        structDef.packageName = packageName;
        foundStructs.set(importedType, structDef);
        
        // 也添加它的依赖项
        for (const dep of structDef.dependencies) {
          if (!processedStructs.has(dep) && !foundStructs.has(dep)) {
            structsToFind.add(dep);
          }
        }
      }
    }
  }

  // 格式化输出
  return formatStructDefinitions(foundStructs);
}

/**
 * 通过名称查找包目录
 * @param packageName 包的名称
 * @param rootDir 要搜索的根目录
 * @returns 包目录的路径，如果未找到则返回null
 */
function findPackageDir(packageName: string, rootDir: string): string | null {
  // 常见的包位置
  const commonLocations = [
    path.join(rootDir, packageName),
    path.join(rootDir, 'common', packageName),
    path.join(rootDir, 'domain', packageName),
    path.join(rootDir, 'interface_admin', packageName),
    path.join(rootDir, 'interface_client', packageName),
    path.join(rootDir, 'application', packageName),
  ];
  
  // 首先检查常见位置
  for (const location of commonLocations) {
    if (fs.existsSync(location) && fs.statSync(location).isDirectory()) {
      return location;
    }
  }
  
  // 如果在常见位置未找到，则递归搜索
  return findPackageDirRecursive(packageName, rootDir);
}

/**
 * 递归搜索包目录
 * @param packageName 包的名称
 * @param dir 要搜索的目录
 * @returns 包目录的路径，如果未找到则返回null
 */
function findPackageDirRecursive(packageName: string, dir: string): string | null {
  try {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    // 首先检查这个目录是否是我们要找的包
    const goFiles = entries.filter(entry => entry.isFile() && entry.name.endsWith('.go'));
    if (goFiles.length > 0) {
      const content = fs.readFileSync(path.join(dir, goFiles[0].name), 'utf8');
      const packageMatch = /package\s+(\w+)/.exec(content);
      if (packageMatch && packageMatch[1] === packageName) {
        return dir;
      }
    }
    
    // 然后检查子目录
    for (const entry of entries) {
      if (entry.isDirectory()) {
        const result = findPackageDirRecursive(packageName, path.join(dir, entry.name));
        if (result) {
          return result;
        }
      }
    }
  } catch (error) {
    // 忽略错误（例如，权限被拒绝）
  }
  
  return null;
}

/**
 * 在项目中查找结构体定义
 * @param structName 要查找的结构体名称
 * @param rootDir 项目的根目录
 * @returns 结构体定义，如果未找到则返回null
 */
function findStructInProject(structName: string, rootDir: string): StructDefinition | null {
  // 检查是否是限定名称（package.Type）
  if (structName.includes('.')) {
    const [packageName, typeName] = structName.split('.');
    
    // 查找包目录
    const packageDir = findPackageDir(packageName, rootDir);
    if (packageDir) {
      return findStructInFiles(getAllGoFiles(packageDir), typeName);
    }
    
    return null;
  }
  
  // 否则，搜索所有Go文件
  return findStructInFiles(getAllGoFiles(rootDir), structName);
}

/**
 * 在文件列表中查找结构体定义
 * @param files 要搜索的文件列表
 * @param structName 要查找的结构体名称
 * @returns 结构体定义，如果未找到则返回null
 */
function findStructInFiles(files: string[], structName: string): StructDefinition | null {
  for (const file of files) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const structMatch = findStructInContent(content, structName, file);
      
      if (structMatch) {
        return structMatch;
      }
    } catch (error) {
      // 忽略错误（例如，权限被拒绝）
    }
  }
  
  return null;
}

/**
 * 递归获取目录中的所有Go文件
 * @param dir 要搜索的目录
 * @returns 文件路径数组
 */
function getAllGoFiles(dir: string): string[] {
  const files: string[] = [];
  
  function traverse(currentDir: string) {
    try {
      const entries = fs.readdirSync(currentDir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(currentDir, entry.name);
        
        if (entry.isDirectory()) {
          traverse(fullPath);
        } else if (entry.isFile() && entry.name.endsWith('.go')) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // 忽略错误（例如，权限被拒绝）
    }
  }
  
  traverse(dir);
  return files;
}

/**
 * 在文件内容中查找结构体定义
 * @param content 文件的内容
 * @param structName 要查找的结构体名称
 * @param filePath 文件的路径（用于调试）
 * @returns 结构体定义，如果未找到则返回null
 */
function findStructInContent(content: string, structName: string, filePath: string): StructDefinition | null {
  // 获取包名
  const packageMatch = /package\s+(\w+)/.exec(content);
  const packageName = packageMatch ? packageMatch[1] : '';
  
  // 查找结构体定义的开始，但排除被注释的结构体
  // 使用正则表达式查找所有可能的结构体定义
  const structRegex = new RegExp(`(^|\\n)\\s*(//.*\\n)*\\s*type\\s+${structName}\\s+struct\\s*{`, 'g');
  
  let startMatch;
  let validMatch: RegExpExecArray | null = null;
  
  // 查找所有匹配项
  while ((startMatch = structRegex.exec(content)) !== null) {
    // 检查这个匹配是否在注释块内
    const matchPos = startMatch.index;
    
    // 检查这一行之前是否有注释标记 (// 或 /*)
    let isCommented = false;
    
    // 向前查找最近的换行符或文件开始
    let lineStart = matchPos;
    while (lineStart > 0 && content[lineStart - 1] !== '\n') {
      lineStart--;
    }
    
    // 检查这一行是否以注释开始
    const line = content.substring(lineStart, matchPos + startMatch[0].length);
    if (/^\s*\/\//.test(line) || /^\s*\/\*/.test(line)) {
      isCommented = true;
    }
    
    // 如果不在注释块内，这是一个有效的匹配
    if (!isCommented) {
      validMatch = startMatch;
      break;
    }
  }
  
  // 如果没有找到有效的匹配，返回null
  if (!validMatch) {
    return null;
  }
  
  // 找到了有效的匹配，继续处理
  const startPos = validMatch.index + (validMatch[1]?.length || 0); // 加上前导换行符的长度
  const startOfStructDef = content.indexOf('type', startPos);
  const startOfStructBody = content.indexOf('{', startOfStructDef) + 1;
  
  // 通过计数大括号查找结构体定义的结束
  let braceCount = 1; // 我们已经遇到了开始的大括号
  let endPos = startOfStructBody;
  
  while (braceCount > 0 && endPos < content.length) {
    if (content[endPos] === '{') {
      braceCount++;
    } else if (content[endPos] === '}') {
      braceCount--;
    }
    endPos++;
  }
  
  if (braceCount !== 0) {
    // 找不到匹配的结束大括号
    return null;
  }
  
  // 提取完整的结构体定义
  const structDef = content.substring(startOfStructDef, endPos);
  
  // 查找依赖项
  const dependencies: string[] = [];
  const structBody = content.substring(startOfStructBody, endPos - 1);
  
  // 获取导入以解析包别名
  const imports = getImports(content);
  const aliasMap = new Map<string, string>();
  
  for (const imp of imports) {
    const pkgName = imp.path.split('/').pop() || '';
    aliasMap.set(imp.alias || pkgName, pkgName);
  }
  
  // 检查嵌入的结构体（只有类型名称而没有字段名称的行）
  const embeddedRegex = /^\s+(\w+(?:\.\w+)?)(?:\s+`.*?`)?$/gm;
  let embeddedMatch;
  while ((embeddedMatch = embeddedRegex.exec(structBody)) !== null) {
    const typeName = embeddedMatch[1];
    if (!/^(string|int|int64|float|float64|bool|interface|map|byte|rune|uint|uint64)$/.test(typeName)) {
      // 如果是限定名称，解析包别名
      if (typeName.includes('.')) {
        const [pkgAlias, typeNamePart] = typeName.split('.');
        const resolvedPkg = aliasMap.get(pkgAlias) || pkgAlias;
        dependencies.push(`${resolvedPkg}.${typeNamePart}`);
      } else {
        dependencies.push(typeName);
      }
    }
  }
  
  // 检查带有类型名称的常规字段
  const fieldRegex = /\s+\w+\s+(?:\*)?(\w+(?:\.\w+)?)(?:\s+`.*?`)?/g;
  let fieldMatch;
  while ((fieldMatch = fieldRegex.exec(structBody)) !== null) {
    const typeName = fieldMatch[1];
    // 只添加自定义类型（不是内置类型如string, int等）
    if (!/^(string|int|int64|float|float64|bool|interface|map|byte|rune|uint|uint64)$/.test(typeName)) {
      // 如果是限定名称，解析包别名
      if (typeName.includes('.')) {
        const [pkgAlias, typeNamePart] = typeName.split('.');
        const resolvedPkg = aliasMap.get(pkgAlias) || pkgAlias;
        dependencies.push(`${resolvedPkg}.${typeNamePart}`);
      } else {
        dependencies.push(typeName);
      }
    }
  }
  
  // 还要检查像[]*TypeName这样的切片类型
  const sliceRegex = /\s+\w+\s+\[\](?:\*)?(\w+(?:\.\w+)?)(?:\s+`.*?`)?/g;
  while ((fieldMatch = sliceRegex.exec(structBody)) !== null) {
    const typeName = fieldMatch[1];
    if (!/^(string|int|int64|float|float64|bool|interface|map|byte|rune|uint|uint64)$/.test(typeName)) {
      // 如果是限定名称，解析包别名
      if (typeName.includes('.')) {
        const [pkgAlias, typeNamePart] = typeName.split('.');
        const resolvedPkg = aliasMap.get(pkgAlias) || pkgAlias;
        dependencies.push(`${resolvedPkg}.${typeNamePart}`);
      } else {
        dependencies.push(typeName);
      }
    }
  }
  
  // 还要检查像map[string]TypeName这样的映射类型
  const mapRegex = /\s+\w+\s+map\[\w+\](?:\*)?(\w+(?:\.\w+)?)(?:\s+`.*?`)?/g;
  while ((fieldMatch = mapRegex.exec(structBody)) !== null) {
    const typeName = fieldMatch[1];
    if (!/^(string|int|int64|float|float64|bool|interface|map|byte|rune|uint|uint64)$/.test(typeName)) {
      // 如果是限定名称，解析包别名
      if (typeName.includes('.')) {
        const [pkgAlias, typeNamePart] = typeName.split('.');
        const resolvedPkg = aliasMap.get(pkgAlias) || pkgAlias;
        dependencies.push(`${resolvedPkg}.${typeNamePart}`);
      } else {
        dependencies.push(typeName);
      }
    }
  }
  
  return {
    name: structName,
    content: structDef,
    dependencies,
    packageName
  };
}

/**
 * 从文件中获取导入语句
 * @param content 文件的内容
 * @returns 导入信息数组
 */
function getImports(content: string): { alias: string; path: string }[] {
  const imports: { alias: string; path: string }[] = [];
  
  // 匹配单个导入：import "package/path"
  const singleImportRegex = /import\s+(?:(\w+)\s+)?"([^"]+)"/g;
  let match;
  
  while ((match = singleImportRegex.exec(content)) !== null) {
    imports.push({
      alias: match[1] || '',
      path: match[2]
    });
  }
  
  // 匹配分组导入：import ( ... )
  const groupImportRegex = /import\s+\(([\s\S]*?)\)/g;
  let groupMatch;
  
  while ((groupMatch = groupImportRegex.exec(content)) !== null) {
    const importBlock = groupMatch[1];
    const importLineRegex = /(?:(\w+)\s+)?"([^"]+)"/g;
    let importMatch;
    
    while ((importMatch = importLineRegex.exec(importBlock)) !== null) {
      imports.push({
        alias: importMatch[1] || '',
        path: importMatch[2]
      });
    }
  }
  
  return imports;
}

/**
 * 格式化结构体定义输出
 * @param structs 结构体定义的映射
 * @returns 格式化的输出
 */
function formatStructDefinitions(structs: Map<string, StructDefinition>): string {
  // 对结构体进行排序，使依赖项在使用它们的结构体之前
  const sortedStructs: StructDefinition[] = [];
  const added: Set<string> = new Set();
  
  function addStruct(name: string) {
    if (added.has(name) || !structs.has(name)) {
      return;
    }
    
    const struct = structs.get(name)!;
    
    // 首先添加依赖项
    for (const dep of struct.dependencies) {
      addStruct(dep);
    }
    
    sortedStructs.push(struct);
    added.add(name);
  }
  
  // 从主结构体开始
  for (const [name] of structs) {
    addStruct(name);
  }
  
  // 格式化输出
  return sortedStructs.map(struct => struct.content).join('\n\n');
}


// const result = findStructDefinitions('c:/project/trpc-project/streamer_backend', 'getUserStatisticalChartRsp');
// console.log(result);
