import winston from 'winston';
import path from 'path';
import dayjs from 'dayjs';
import 'winston-daily-rotate-file';

const { combine, timestamp, printf, colorize, align } = winston.format;

// 定义日志格式
const logFormat = printf(({ level, message, timestamp }) => {
  return `${dayjs(timestamp as string).format('YYYY-MM-DD HH:mm:ss')} [${level}]: ${message}`;
});

// 创建日志目录路径
export const logDir = path.join(__dirname, '../logs');

export const logger = winston.createLogger({
  level: 'info',
  format: combine(
    timestamp(),
    logFormat
  ),
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: combine(
        colorize({ all: true }),
        align(),
        logFormat
      )
    }),
    // 错误日志 - 按日期轮转
    new winston.transports.DailyRotateFile({
      filename: path.join(logDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxSize: '5m',
      maxFiles: '7d',
      auditFile: path.join(logDir, 'audit', 'combined-audit.json'),
    }),
    // 组合日志 - 按日期轮转
    new winston.transports.DailyRotateFile({
      filename: path.join(logDir, 'combined-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '5m',
      maxFiles: '7d',
      auditFile: path.join(logDir, 'audit', 'error-audit.json'),
    })
  ]
});

