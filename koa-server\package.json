{"name": "koa-server", "version": "1.0.0", "description": "A Koa server with chat completions API", "main": "app.js", "scripts": {"start": "node app.js", "start:pm2": "pm2 start ecosystem.config.js", "stop:pm2": "pm2 stop ecosystem.config.js", "restart:pm2": "pm2 restart ecosystem.config.js", "reload:pm2": "pm2 reload ecosystem.config.js", "delete:pm2": "pm2 delete ecosystem.config.js", "logs:pm2": "pm2 logs koa-server", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "dayjs": "^1.11.13", "koa": "^2.14.2", "koa-bodyparser": "^4.4.1", "koa-router": "^12.0.1", "pm2": "^6.0.6", "winston": "^3.17.0"}}