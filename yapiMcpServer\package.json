{"name": "@tencent/mcp-yapi", "version": "1.0.1", "devDependencies": {"@types/minimist": "^1.2.5", "@types/node": "^22.15.17", "rimraf": "^5.0.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "files": ["dist"], "bin": {"mcp-yapi": "dist/index.js"}, "scripts": {"build": "rimraf dist && tsc", "dev": "ts-node src/index.ts", "start": "node dist/yapiServer.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.2", "axios": "^1.9.0", "dayjs": "^1.11.13", "minimist": "^1.2.8", "username": "^7.0.0", "uuidv4": "^6.2.13", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.4"}}