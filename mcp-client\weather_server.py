import os
import asyncio
import httpx
import uuid
import hmac
import base64
import hashlib
from typing import Any
from datetime import datetime
from urllib.parse import urlencode
from mcp.server.fastmcp import FastMCP
from dotenv import load_dotenv

load_dotenv()  # 加载 .env 文件，确保 API Key 受到保护

secretId = os.getenv("tc_weather_secret_id")
secretKey = os.getenv("tc_weather_secret_key")

# 初始化MCP服务器
mcp = FastMCP("WeatherServer", port=9020)


def get_sign():
  dt = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
  sign_str = f"x-date: {dt}"
  sign = base64.b64encode(hmac.new(secretKey.encode('utf-8'), sign_str.encode('utf-8'), hashlib.sha1).digest())
  auth = f'{{"id": "{secretId}", "x-date": "{dt}" , "signature": "{sign.decode("utf-8")}"}}'
  return auth


async def fetch_weather(city: str, weather_day="weather1d") -> dict[str, Any] | None:
  query_params = {
    "areaCn": city,
  }
  url = f'https://ap-guangzhou.cloudmarket-apigw.com/service-6drgk6su/lundear/{weather_day}'
  if query_params:
    url = url + '?' + urlencode(query_params)

  headers = {
    'request-id': str(uuid.uuid1()),
    'Authorization': get_sign()
  }

  async with httpx.AsyncClient() as client:
    try:
      response = await client.get(url, headers=headers)
      response.raise_for_status()
      return response.json()  # 返回字典类型
    except httpx.HTTPStatusError as e:
      return {"error": f"HTTP 错误: {e.response.status_code}"}
    except Exception as e:
      return {"error": f"请求失败: {str(e)}"}


def format_weather(res: dict, weather_day="weather1d") -> str:
  if res["code"] != '0' and res["code"] != 0:
    return f"❌ 天气查询失败: {res['desc']}"

  data = res["data"]
  city = data["cityInfo"]

  def get_info(data_):
    if weather_day == "weather1d":
      temp = data_["temp"]
      humidity = data_["sd"]
      wind_speed = data_["wse"]
      description = data_["weather"]
      return f"""
🌡 温度: {temp}°C"
💧 湿度: {humidity}"
🌬 风速: {wind_speed}"
🌤 天气: {description}"
      """
    else:
      info = ""
      if "temperature_min" in data_:
        info += f"🌡 最小温度: {data_['temperature_min']}\n"
      if "temperature_max" in data_:
        info += f"🌡 最大温度: {data_['temperature_max']}\n"
      if "weather" in data_:
        info += f"🌤 天气: {data_['weather']}\n"
      return info

  if weather_day == "weather1d":
    day_data = data["now"]
    return (
      f"🌍 {city['cityCn']}, {city['provCn']}, {city['nationCn']}\n"
      f"{get_info(day_data)}\n"
    )
  elif weather_day == "weather7d":
    day_list = ["d1", "d2", "d3", "d4", "d5", "d6", "d7"]
    day_result = ""
    for d_key in day_list:
      day_data = data[d_key]
      day_result += f"{get_info(day_data)}\n"
    return (
      f"🌍 {city['cityCn']}, {city['provCn']}, {city['nationCn']}\n"
      f"{day_result}"
    )
  else:
    return "❌ 不支持的天气查询类型。"

@mcp.tool()
async def query_weather(city: str) -> str:
  """
  输入指定城市的英文名称，返回今日天气查询结果。
  :param city: 城市名称（需使用中文，如深圳，北京）
  :return: 格式化后的天气信息
  """
  weather_data = await fetch_weather(city, "weather1d")
  return format_weather(weather_data)


@mcp.tool()
async def query_future_weather(city: str) -> str:
  """
  输入指定城市的英文名称，返回未来7天天气查询结果。
  :param city: 城市名称（需使用中文，如深圳，北京）
  :return: 格式化后的天气信息
  """
  weather_data = await fetch_weather(city, "weather7d")
  return format_weather(weather_data, "weather7d")


if __name__ == "__main__":
  # 以标准 I/O 方式运行 MCP 服务器
  # mcp.run(transport='stdio')
  mcp.run(transport='sse')


# data = asyncio.run(query_weather("深圳"))
# data = asyncio.run(query_future_weather("深圳"))
# print(data)

