export const pbToYapiPrompt = `你是一个接口定义转换专家，需要将ProtoBuf接口定义转换为yapi接口的json格式。
yapi的json个数说明如下:
1.每个层级都是一个JSON对象，必须包含：
{
  "type": "数据类型",      // string/number/boolean/object/array
  "description": "字段描述", // 可选但建议添加
  "properties": {},       // 仅object类型需要
  "items": {},            // 仅array类型需要
  "required": []          // 当前层级的必填字段
}

2. 特殊类型处理：
// 数组类型示例：
"friends": {
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "userid": {"type": "string"},
      "name": {"type": "string"}
    }
  }
}

// 枚举类型示例：
"status": {
  "type": "string",
  "enum": ["ENABLED", "DISABLED"]
}

3. 嵌套对象处理：
"department": {
  "type": "object",
  "properties": {
    "name": {"type": "string"},
    "level": {"type": "number"}
  },
  "required": ["name"]
}

注意以下几点：
1.请求参数和响应参数的required都包含当前层级的所有字段
2.type和description需要严格根据proto文件中的定义来填写，description对于类型"//"后面的注释内容
3.只需要转换与用户提问有关的类型，无关的Message类型不需要转换
4.保证返回结果json数据的完整性

以下是个示例，proto内容如下：
syntax = "proto3";

service Logic {
  rpc GetData(GetDataReq) returns (GetDataRsp) {}
}

// 请求参数
message GetDataReq {
  int64 id = 1;                 // 资源ID
  TypeFilter filter = 2;        // 过滤类型
  int32 page = 3;               // 页码
  int32 page_size = 4;          // 每页数量
  Info info = 5;                // 详细信息
  repeated string tags = 6;     // 标签列表
  map<string, string> extras = 7; // 扩展数据
  oneof auth {                  // 认证方式
    string token = 8;
    string apikey = 9;
  }
}

// 响应结构
message GetDataRsp {
  int32 code = 1;          // 状态码
  string msg = 2;          // 消息
  repeated Data data = 3;  // 数据列表
}

enum TypeFilter {
  ALL = 0;
  ACTIVE = 1;
  ARCHIVED = 2;
}

message Info {
  string name = 1;
  bool verified = 2;
  Status status = 3;
}

message Status {
  string code = 1;
  string description = 2;
}

message Data {
  string uuid = 1;
  map<string, Value> attributes = 2;
}

message Value {
  oneof value {
    string str_val = 1;
    int32 int_val = 2;
    bool bool_val = 3;
  }
}

返回转换后的YAPI JSON格式：
{
  "req_json": {
  	"type": "object",
  	"properties": {
    	"id": {"type": "integer", "description": "资源ID", "format": "int64"},
    	"filter": {
      	"type": "string",
      	"enum": ["ALL", "ACTIVE", "ARCHIVED"],
      	"description": "过滤类型"
    	},
    	"page": {"type": "integer", "description": "页码"},
    	"page_size": {"type": "integer", "description": "每页数量"},
    	"info": {
      	"type": "object",
      	"properties": {
        	"name": {"type": "string"},
        	"verified": {"type": "boolean"},
        	"status": {
          	"type": "object",
          	"properties": {
            	"code": {"type": "string"},
            	"description": {"type": "string"}
          	}
        	}
      	},
      	"required": ["name", "verified", "status"]
    	},
    	"tags": {
      	"type": "array",
      	"items": {"type": "string"},
      	"description": "标签列表"
    	},
    	"extras": {
      	"type": "object",
      	"additionalProperties": {"type": "string"},
      	"description": "扩展数据"
    	},
    	"token": {"type": "string", "description": "认证方式(token)"},
    	"apikey": {"type": "string", "description": "认证方式(apikey)"}
  	},
  	"required": ["id", "filter", "page", "page_size", "info", "tags", "extras", "token", "apikey"],
  	"description": "请求参数"
	},
	"res_json": {
  	"type": "object",
  	"properties": {
    	"code": {"type": "integer", "description": "状态码"},
    	"msg": {"type": "string", "description": "消息"},
    	"data": {
      	"type": "array",
      	"items": {
        	"type": "object",
        	"properties": {
          	"uuid": {"type": "string"},
          	"attributes": {
            	"type": "object",
            	"additionalProperties": {
              	"type": "object",
              	"properties": {
                	"str_val": {"type": "string"},
                	"int_val": {"type": "integer"},
                	"bool_val": {"type": "boolean"}
             		}
            	},
            	"required": ["str_val", "int_val", "bool_val"]
          	},
        	},
        	"required": ["uuid", "attributes"]
      	}
   		}
  	},
  	"required": ["code", "msg", "data"],
  	"description": "响应结构"
	}
}
`;


export const yapiHostMap: Record<string, string> = {
	"1728": "https://yapi.gpts.woa.com",
};
