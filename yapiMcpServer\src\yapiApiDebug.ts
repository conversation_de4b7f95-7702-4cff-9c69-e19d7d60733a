import { Yapi<PERSON><PERSON> } from "./yapiApi";
import * as fs from 'fs';
import { pbToYapiJsonAzure, pbToYapiJsonHy } from "./tools/utils";
import { CommentInterfaceParams, InterfaceParams } from "./types";

const config = JSON.parse(fs.readFileSync('/data/config/yapi-mcp.json', 'utf8'));
const projects = config.projects || [];
const yapiApi = new YapiApi(projects);


async function getProject() {
  const res = await yapiApi.getProject(1728);
  console.log(res);
}

async function getAllInterface() {
  const res = await yapiApi.getAllInterface(1728);
  console.log(res);
}

async function getCatInterface() {
  const res = await yapiApi.getCatInterface(1728, 16705);
  console.log(res);
}

async function getInterfaceDetail() {
  const res = await yapiApi.getInterfaceDetail(1728, 109650);
  console.log(res);
}

async function generateInterfaceTsCode() {
  const res = await yapiApi.generateInterfaceTsCode({
    projectId: 1728,
    interfaceId: 130382,
    templateId: 2,
  });
  console.log(res);
}

async function addYapiInterface() {
  const addParams: InterfaceParams = {
    "type": "add",
    "funcName": "GetPost",
    "currentPb": "git.woa.com/trpcprotocol/pytest/testdemo_logic",
    "modPath": "/project/trpc-project/hellogo/go.mod",
    "projectId": 1187,
    "title": "GetPost接口",
    "path": "/Dynamics/GetPost",
    "catId": 11163,
    "method": "post"
  }
  const res = await yapiApi.addOrUpdateInterface(addParams);
  console.log(res);
}


async function swagSyncYapi() {
  const res = await yapiApi.swagInitAndSyncYapi(1187, '/project/trpc-project/hellogo');
  console.log(res);
}

async function createCategory() {
  const res = await yapiApi.createCategory(1187, '动态列表');
  console.log(res);
}


async function testPbToYapi() {
  const pbContent = "message GetPostDataReq {\n  int32 type = 1;\n  string name = 2;\n  // 引用类型\n  trpc.publishing_application.standalonesite.PageInfo page_info = 3;\n}\nmessage GetPostDataRsp {\n  int32 count = 1;\n  // 引用类型\n  repeated trpc.publishing_application.standalonesite.RepliesItem list = 3;\n}\nmessage PageInfo {\n  string previous_page_cursor = 1;\n  string next_page_cursor = 2;\n  bool is_finish = 3;\n  int32 total = 4;\n}\nmessage RepliesItem {\n  string comment_uuid = 1;\n  string post_uuid = 2;\n  string reply_uuid = 3;\n  string intl_openid = 4;\n  int32 is_audit = 5;\n  bool is_star = 6;\n  int32 is_parent_del = 7;\n  string at_intl_openid = 8;\n  string game_id = 9;\n  string area_id = 10;\n  string title = 11;\n  string content = 12;\n  repeated string pic_urls = 13;\n  int64 upvote_count = 14;\n  int64 created_on = 15;\n  int64 modified_on = 16;\n  bool can_delete = 17;\n  bool can_report = 18;\n  bool is_mine = 19;\n  bool is_author = 20;\n  // 引用类型\n  trpc.publishing_application.standalonesite.UserInfo user = 21;\n  // 引用类型\n  trpc.publishing_application.standalonesite.UserInfo at_user = 22;\n}\nmessage UserInfo {\n  int64 id = 1;\n  string username = 2;\n  int32 status = 3;\n  string avatar = 4;\n  bool is_admin = 5;\n  string intl_openid = 6;\n  string remark = 7;\n  string language = 8;\n  // 引用类型\n  UserBindTitle titles = 9;\n  int32 fans_num = 10;\n  int32 follow_num = 11;\n  int32 post_num = 12;\n  int32 all_post_num = 13;\n  bool is_mute = 14;\n  int32 auth_type = 15;\n  string mood = 16;\n  int32 game_tag = 17;\n  string home_page_links = 18;\n  string audit_username = 19;\n  string audit_remark = 20;\n  bool is_audit_username = 21;\n  bool is_audit_remark = 22;\n  bool has_sign_privacy = 23;\n  bool is_first_register = 24;\n  int32 game_tag_num = 25;\n  bool had_modified_username = 26;\n  string auth_desc = 27;\n  string avatar_pendant = 28;\n  int32 is_followed = 29;\n  // 引用类型\n  repeated LanguageItem auth_languages = 30;\n  // 引用类型\n  repeated UserInfoLanguageItem user_infos_languages = 31;\n  int32 created_on = 32;\n  int32 is_mutual_follow = 33;\n  int32 game_adult_status = 34;\n  int32 is_black = 35;\n  repeated string regions = 36;\n}\nmessage UserBindTitle {\n  int64 id = 1;\n  // 引用类型\n  TitleNewList title = 2;\n  string intl_openid = 3;\n  int64 title_id = 4;\n  int64 status = 5;\n  string game_id = 6;\n  string area_id = 7;\n}\nmessage LanguageItem {\n  string language = 1;\n  string desc = 2;\n}\nmessage UserInfoLanguageItem {\n  string language = 1;\n  string desc = 2;\n  int32 type = 3;\n}\nmessage TitleNewList {\n  int64 id = 1;\n  // 引用类型\n  TitleLanguage language = 2;\n  string avatar = 3;\n  int64 up_time = 4;\n  int64 down_time = 5;\n  int64 init_hot = 6;\n  int64 possess_num = 7;\n  int64 status = 8;\n  string game_id = 9;\n  string area_id = 10;\n}\nmessage TitleLanguage {\n  int64 id = 1;\n  string language = 2;\n  int64 title_id = 3;\n  string title = 4;\n  string introduce = 5;\n}\nservice Logic {\n  rpc GetPost (GetPostDataReq) returns (GetPostDataRsp) {\n  }\n}\n"
  const t1 = Date.now();
  const data = await pbToYapiJsonAzure("GetPost", pbContent);
  // const data = await pbToYapiJsonHy("GetPost", pbContent);
  console.log('cost:', Date.now() - t1, 'ms');
  console.log('data', data);
}

async function addOrUpCommentInterface() {
  const params: CommentInterfaceParams = {
    type: "add",
    projectId: 1187,
    projectPath: "c:/project/trpc-project/streamer_backend",
    interfaceId: 0,
    title: "获取dashboard任务详情页面数据",
    tags: "数据中心",
    desc: "获取dashboard任务详情页面数据",
    method: "post",
    path: "/creatorhub/api/admin/v2/dashboard/get_task_content",
    reqType: "getTaskContentReq",
    resType: "getTaskContentRsp"
  }
  const res = await yapiApi.addOrUpCommentInterface(params);
  console.log(res);
}

// testPbToYapi();
// createCategory();
// swagSyncYapi();
addOrUpCommentInterface();
