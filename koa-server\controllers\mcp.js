const axios = require('axios');
const protobuf = require('protobufjs');
const path = require('path');
const fs = require('fs');
const simpleGit = require('simple-git');
const os = require('os');

async function pbParse(ctx) {
  const { reqType, resType, pbUrl } = ctx.request.body;
  if (!reqType || !resType || !pbUrl) {
    ctx.body = { code: 400, message: '缺少参数', pbContent: '' };
    return;
  }

  const protosUrl = `${pbUrl.split('/').slice(0, -1).join('/')}.git`;

  // 从pbUrl解析pbDir和pbFile（最后一级目录名）
  // 例如 pbUrl = 'e.coding.intlgame.com/ptc/aix-backend/protos/material_display'
  const pbDir = path.basename(pbUrl);
  const pbFile = pbDir + '.proto';

  // 1. 生成临时目录
  const tmpDir = path.join(os.tmpdir(), 'pbParse_aix');

  // 2. 拼接带账号密码的git url
  let gitUrl = protosUrl;
  if (!/^https?:\/\//.test(gitUrl)) gitUrl = 'https://' + gitUrl;
  gitUrl = gitUrl.replace('https://', 'https://pyongchen:hao8r5sT%40py@');

  console.log('gitUrl', gitUrl);
  console.log('tmpDir', tmpDir);

  try {
    // 3. clone 仓库
    if (!fs.existsSync(tmpDir)) {
      fs.mkdirSync(tmpDir);
      await simpleGit().clone(gitUrl, tmpDir);
    } else {
      // 如果目录存在，则拉取最新代码
      await simpleGit(tmpDir).pull();
    }

    // 4. 查找主proto文件（自动解析目录和文件名）
    const mainProto = path.join(tmpDir, pbDir, pbFile);
    console.log('mainProto', mainProto);
    if (!fs.existsSync(mainProto)) {
      return { code: 500, message: `未找到主proto文件: ${pbDir}/${pbFile}`, pbContent: '' };
    }

    // 5. 递归收集所有依赖proto内容
    const visited = new Set();
    function collectProto(filePath, baseDir) {
      console.log('filePath', filePath, 'baseDir', baseDir);
      if (visited.has(filePath)) return '';
      visited.add(filePath);
      let content = fs.readFileSync(filePath, 'utf8');
      // 递归import
      content = content.replace(/^import\s+"([^"]+)";/mg, (m, importPath) => {
        const depPath = path.resolve(path.dirname(filePath), '../', importPath);
        return collectProto(depPath, baseDir);
      });
      return content;
    }
    const pbContent = collectProto(mainProto, tmpDir);

    // 6. 保存合并后的proto到临时文件
    const mergedProtoPath = path.join(tmpDir, 'merged.proto');
    fs.writeFileSync(mergedProtoPath, pbContent, 'utf8');

    // 7. 加载合并proto
    const root = await protobuf.load(mergedProtoPath);
    const ReqType = root.lookupType(reqType);
    const ResType = root.lookupType(resType);
    const reqObj = ReqType.create();
    const resObj = ResType.create();
    const reqBuffer = ReqType.encode(reqObj).finish();
    const resBuffer = ResType.encode(resObj).finish();
    
    return {
      code: 200,
      message: 'success',
      pbContent,
      reqExample: reqBuffer.toString('base64'),
      resExample: resBuffer.toString('base64'),
    };

    // 8. 清理临时目录
    // fs.rmSync(tmpDir, { recursive: true, force: true });
  } catch (err) {
    return { code: 500, message: err.message, pbContent: '' };
  }
}

module.exports = {
  pbParse,
};