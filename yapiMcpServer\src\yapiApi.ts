import axios from "axios";
import dayjs from "dayjs";
import fs from "fs";
import {
  GetInterfaceDetailParams, InterfaceParams, ProjectConfig, CatItem, YapiIntefaceItem,
  CommentInterfaceParams,
} from './types';
import { logger } from './logger';
import { pbToYapiJsonAzure, getAbsolutePath, isSwagInstalled, executeSwagInit } from "./tools/utils";
import { findStructDefinitions } from "./tools/finder";
import { yapiHostMap } from "./tools/const";

const errMsg = '调用过程中发生错误，终止后续任务';

const pluginAdminHost = "https://yapi.spraylee.com"; // 插件管理后台接口
const pbAdminHost = "http://************:8801"; // pb管理后台接口，解析pb文件

export class YapiApi {
  projects: ProjectConfig[];

  constructor(projects: ProjectConfig[]) {
    this.projects = projects;
  }

  // 获取项目信息，项目名称和接口分类
  async getProject(projectId: number) {
    const target = this.projects.find((item) => item.project_id === projectId)!;
    const res = await axios.get(`${target.yapi_host}/api/project/get`, {
      params: {
        id: projectId,
        token: target.token,
      },
    });
    return res.data.data as {
      name: string;
      cat: CatItem[];
    };
  }

  // 创建分类，先判断分类是否存在
  async createCategory(projectId: number, catName: string): Promise<CatItem> {
    logger.info(`createCategory, projectId:${projectId}，catName:${catName}`);
    const project = await this.getProject(projectId);
    const cat = project.cat.find((item: CatItem) => item.name === catName);
    if (cat) {
      logger.info(`分类${catName}已存在，id:${cat._id}`);
      return {id: cat._id, _id: cat._id, name: cat.name};
    }
    const target = this.projects.find((item) => item.project_id === projectId)!;
    const res = await axios.post(`${target.yapi_host}/api/interface/add_cat?token=${target.token}`, {
      project_id: projectId,
      name: catName,
    });
    const data = res.data.data as { _id: number, name: string };
    return {id: data._id, _id: data._id, name: data.name};
  };

  // 获取所有接口列表
  async getAllInterface(projectId: number, keyword?: string): Promise<string | undefined> {
    const target = this.projects.find((item) => item.project_id === projectId)!;
    const res = await axios.get(`${target.yapi_host}/api/interface/list`, {
      params: {
        token: target.token,
        project_id: projectId,
        page: 1,
        limit: 1000,
      },
    });
    const { data } = res;
    if (data.errcode !== 0) {
      logger.error(`getAllInterface失败: ${data.errmsg}`);
      return;
    }
    let { list } = data.data;
    let result = "";
    // 根据关键词过滤接口，忽略大小写
    if (keyword) {
      list = list.filter((item: any) => item.title.toLowerCase().includes(keyword.toLowerCase()));
    }
    for (const item of list) {
      const createTime = dayjs(item.add_time * 1000).format("YYYY-MM-DD HH:mm");
      result += `接口id:${item._id}; 分类id:${item.catid}; 名称:${item.title}; 创建时间:${createTime}; 方法:${item.method} 路径: ${item.path} \n`;
    }
    return result;
  }

  // 获取某个分类下接口列表
  async getCatInterface(
    projectId: number,
    catId: number
  ): Promise<string | undefined> {
    const target = this.projects.find((item) => item.project_id === projectId)!;
    const res = await axios.get(`${target.yapi_host}/api/interface/list_cat`, {
      params: {
        token: target.token,
        catid: catId,
        page: 1,
        limit: 1000,
      },
    });
    const { data } = res;
    if (data.errcode !== 0) {
      console.log(data.errmsg);
      return;
    }
    const { list } = data.data;
    let result = "";
    for (const item of list) {
      const createTime = dayjs(item.add_time * 1000).format("YYYY-MM-DD HH:mm");
      result += `接口id:${item._id}; 分类id:${item.catid}; 名称:${item.title}; 创建时间:${createTime}; 方法:${item.method} 路径: ${item.path} \n`;
    }
    return result;
  }

  // 获取接口详情
  async getInterfaceDetail(projectId: number, interfaceId: number): Promise<any> {
    const target = this.projects.find((item) => item.project_id === projectId)!;
    const res = await axios.get(`${target.yapi_host}/api/interface/get`, {
      params: {
        token: target.token,
        id: interfaceId,
      },
    });
    const { data } = res;
    if (data.errcode !== 0) {
      console.log(`获取yapi接口失败：${data.errmsg}`);
      return null;
    }
    return data.data;
  }

  // 添加或更新接口
  async addOrUpdateInterface(params: InterfaceParams) {
    logger.info(`addOrUpdateInterface接口参数: ${JSON.stringify(params)}`);
    const target = this.projects.find((item) => item.project_id === params.projectId)!;

    let modContent = '';

    // 读取本地mod文件
    const modPath = getAbsolutePath(params.modPath);
    if (modPath) {
      try {
        modContent = fs.readFileSync(modPath, 'utf8');
      } catch (error) {
        logger.error(`读取go.mod文件失败: ${modPath}`);
        return errMsg;
      }
    }

    if (!modContent) {
      logger.error(`go.mod文件为空: ${modPath}`);
      return errMsg;
    }

    const pbRes = await axios.post(`${pbAdminHost}/PreseProto`, {
      current_pb: params.currentPb,
      func_name: params.funcName,
      mod_content: modContent,
    }).catch((err) => err.response);

    const pbData = pbRes.data;
    if (pbData.error) {
      logger.error(`解析pb失败: ${pbData.error}, params: ${JSON.stringify(params)}`);
      return errMsg;
    }
    logger.info(`pbData: ${JSON.stringify(pbData)}`);

    const { proto } = pbData;
    const yapiRes = await pbToYapiJsonAzure(params.funcName, proto);
    if (!yapiRes) {
      logger.error(`Ai生成yapi接口失败`);
      return errMsg;
    }

    logger.info(`yapi接口参数: ${JSON.stringify(yapiRes)}`);

    const { title, path, catId, method} = params;
    const { req_json: reqBody, res_json: resBody} = yapiRes;

    let interfacePath = path || `${target.server_path}/${params.funcName}`;

    let resMsg;
    if (params.type === 'add') {
      resMsg = await this.addYapiInterface(params.projectId, title, interfacePath, catId, method, reqBody, resBody);
    } else {
      resMsg = await this.upYapiInterface(params.projectId, params.interfaceId as number, title, interfacePath, reqBody, resBody);
    }
    return resMsg;
  }

  // 添加yapi接口
  async addYapiInterface(
    projectId: number, title: string, path: string, catid: number, method: string,
    reqBody: Record<string, any>, resBody: Record<string, any>,
  ): Promise<any> {
    const target = this.projects.find((item) => item.project_id === projectId)!;

    // 先创建空白接口，获取id
    const addParams = {
      catid, method, path, title, project_id: projectId,
    };
    logger.info(`添加yapi接口参数: ${JSON.stringify(addParams)}`);
    const res = await axios.post(`${target.yapi_host}/api/interface/add?token=${target.token}`, addParams);
    const { data } = res;
    logger.info(`添加yapi接口返回: ${JSON.stringify(data)}`);
    if (data.errcode == 0) {
      const { _id } = data.data; // 新的接口id
      const yapiUrl = `${target.yapi_host}/project/${projectId}/interface/api/${_id}`;
      // 添加成功，需要继续编辑接口，录入请求和返回参数类型

      const updateParams = {
        catid,
        id: _id,
        req_body_is_json_schema: true,
        req_body_type: "json",
        req_body_other: JSON.stringify(reqBody),
        res_body: JSON.stringify(resBody),
        res_body_is_json_schema: true,
        res_body_type: "json",
        req_headers: [{name: "Content-Type", value: "application/json"}],
      }
      logger.info(`更新yapi接口参数: ${JSON.stringify(updateParams)}`);
      const yapiHost = yapiHostMap[String(target.project_id)] || target.yapi_host;
      const upRes = await axios.post(`${yapiHost}/api/interface/up?token=${target.token}`, updateParams);
      logger.info(`更新yapi接口返回: ${JSON.stringify(data)}`);
      return upRes.data.errcode == 0 ? `创建成功: ${yapiUrl}` : "创建失败";
    } else {
      return `添加接口失败: ${data.errmsg}`;
    }
  }

  // 更新yapi接口
  async upYapiInterface(
    projectId: number, interfaceId: number, title: string, path: string, reqBody: any, resBody: any,
  ): Promise<any> {
    const target = this.projects.find((item) => item.project_id === projectId)!;
    const yapiHost = yapiHostMap[String(target.project_id)] || target.yapi_host;
    const yapiUrl = `${yapiHost}/project/${projectId}/interface/api/${interfaceId}`;
    logger.info(`更新接口req_body_other: ${JSON.stringify(reqBody)}`);
    logger.info(`更新接口res_body: ${JSON.stringify(resBody)}`);
    const upRes = await axios.post(`${target.yapi_host}/api/interface/up?token=${target.token}`, {
      id: interfaceId,
      title,
      path,
      req_body_is_json_schema: true,
      req_body_type: "json",
      req_body_other: JSON.stringify(reqBody),
      res_body: JSON.stringify(resBody),
      res_body_is_json_schema: true,
      res_body_type: "json",
      req_headers: [{name: "Content-Type", value: "application/json"}],
    });
    logger.info(`更新yapi接口返回: ${JSON.stringify(upRes.data)}`);
    return upRes.data.errcode == 0 ? `更新成功: ${yapiUrl}` : `更新失败: ${upRes.data.errmsg}`;
  }

  private async generateCodeSync(params: any, tryTime = 1): Promise<string> {
    const res = await axios.post(
      `${pluginAdminHost}/trpc/generate.generateCodeSync?batch=1`,
      params,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const [data] = res.data;
    if (data.error) {
      if (tryTime > 3) return `生成代码失败: ${data.error.message}`;
      else return this.generateCodeSync(params, tryTime + 1);
    } else {
      return data.result.data.code;
    }
  }

  // 调用插件管理端生成代码
  async generateInterfaceTsCode(params: GetInterfaceDetailParams): Promise<string> {
    logger.info(`generateInterfaceTsCode接口参数: ${JSON.stringify(params)}`);
    const { projectId, templateId, interfaceId } = params;
    const interfaceData = await this.getInterfaceDetail(projectId, interfaceId);
    if (!interfaceData) {
      return "获取接口信息失败";
    }
    // const curUser = await username();
    const res = await this.generateCodeSync({
      type: "mutation",
      0: {
        aiConfig: {},
        projectId: projectId,
        apiId: interfaceId,
        apiSpec: JSON.stringify(interfaceData),
        templateId: templateId,
        username: "pyongchen",
        version: "0.2.0",
      },
    });
    return res;
  }

  async addOrUpCommentInterface(params: CommentInterfaceParams): Promise<string> {
    logger.info(`addOrUpdateCommentInterface接口参数: ${JSON.stringify(params)}`);
    const { projectId, projectPath, type, interfaceId, title, tags, desc, method, path, reqType, resType } = params;
    const target = this.projects.find((item) => item.project_id === projectId)!;

    const reqTypeContent = await findStructDefinitions(projectPath, reqType);
    const resTypeContent = await findStructDefinitions(projectPath, resType);

    const catItem = await this.createCategory(projectId, tags);
    
    const functionName = 'Interface';
    const pbContent = `
syntax = "proto3";

${reqTypeContent}

${resTypeContent}

service Logic {
  rpc ${functionName}(${reqType}) returns (${reqType}) {}
}
`;
    console.log('comment pbContent', pbContent);
    logger.info(`yapi接口参数: ${pbContent}`);
    const yapiRes = await pbToYapiJsonAzure(functionName, pbContent);
    if (!yapiRes) {
      logger.error(`Ai生成yapi接口失败`);
      return errMsg;
    }

    logger.info(`yapi接口参数: ${JSON.stringify(yapiRes)}`);

    const { req_json: reqBody, res_json: resBody} = yapiRes;

    let resMsg;
    if (params.type === 'add') {
      resMsg = await this.addYapiInterface(params.projectId, title, path, catItem.id, method, reqBody, resBody);
    } else {
      resMsg = await this.upYapiInterface(params.projectId, params.interfaceId as number, title, path, reqBody, resBody);
    }
    return resMsg;
  }

  async swagInitAndSyncYapi(projectId: number, projectPath: string): Promise<string> {
    logger.info(`swagInitAndSyncYapi接口参数: projectId: ${projectId}, projectPath: ${projectPath}`);

    try {
      // 检查swag是否已安装
      const swagInstalled = await isSwagInstalled();
      if (!swagInstalled) {
        const errorMsg = '请先安装swag命令: go install github.com/swaggo/swag/cmd/swag@latest';
        logger.error(errorMsg);
        return errorMsg;
      }

      // 执行swag init命令生成swagger文档
      const swagResult = await executeSwagInit(projectPath);
      if (!swagResult.success) {
        logger.error(swagResult.message);
        return swagResult.message;
      } else {
        logger.info(swagResult.message);
      }

      // 检查文件是否存在
      if (!swagResult.swaggerPath || !fs.existsSync(swagResult.swaggerPath)) {
        const errorMsg = `swagger.json文件不存在: ${swagResult.swaggerPath}`;
        logger.error(errorMsg);
        return errorMsg;
      }

      logger.info(`swagger.json文件生成成功: ${swagResult.swaggerPath}`);

      // 导入swagger数据到yapi
      const importSuccess = await this.importSwaggerYapi(projectId, swagResult.swaggerPath);

      // 删除生成的swagger docs文件夹
      const docsPath = `${projectPath}/docs`;
      if (fs.existsSync(docsPath)) {
        fs.rmSync(docsPath, { recursive: true });
      }

      return `同步结果: ${importSuccess ? '成功' : '失败'}`;
    } catch (error) {
      const errorMsg = `swagInitAndSyncYapi执行过程中发生错误: ${error}`;
      logger.error(errorMsg);
      return errorMsg;
    }
  }

  // 从swagger文件导入yapi接口
  async importSwaggerYapi(projectId: number, swaggerPath: string) {
    logger.info(`importSwaggerYapi接口参数: projectId: ${projectId}, swaggerPath: ${swaggerPath}`);
    const target = this.projects.find((item) => item.project_id === projectId)!;
    const project = await this.getProject(projectId);
    const { cat: allCatList } = project;

    const swaggerContent = fs.readFileSync(swaggerPath, 'utf8');
    const swaggerData = JSON.parse(swaggerContent);

    // 先收集所有分类
    const curCatList: CatItem[] = [];
    const interfaceList = [];
    for (const pathKey in swaggerData.paths) {
      if (!swaggerData.paths.hasOwnProperty(pathKey)) continue;
      const path = swaggerData.paths[pathKey];
      for (const method in path) {
        if (!path.hasOwnProperty(method)) continue;
        const endpoint = path[method];
        const {summary, description, tags: [tagName], parameters, responses} = endpoint;
        if (!curCatList.find((item) => item.name === tagName)) {
          curCatList.push({name: tagName, _id: 0, id: 0});
        }
        interfaceList.push({
          method, path: pathKey, summary, description, catName: tagName, parameters, responses,
        });
      }
    }

    // 未添加的分类需要先创建
    for (const cat of curCatList) {
      const catItem = allCatList.find((item) => item.name === cat.name);
      if (!catItem) {
        const newCat = await this.createCategory(projectId, cat.name);
        allCatList.push(newCat);
      }
    }

    // 遍历所有路径
    const targetList: YapiIntefaceItem[] = [];
    interfaceList.forEach((item) => {
      const { method, path, summary, description, catName, parameters, responses } = item;
      const catid = allCatList.find((item) => item.name === catName)!._id;

      // 获取请求体定义
      const reqRef = parameters[0]?.schema?.$ref;
      const reqDefinitionKey = reqRef?.split('/')[2]; // 从#/definitions/xxx提取xxx
      const reqBody = swaggerData.definitions[reqDefinitionKey];

      // 获取响应体定义
      const resRef = responses['200']?.schema?.$ref;
      const resDefinitionKey = resRef?.split('/')[2];
      const resBody = swaggerData.definitions[resDefinitionKey];

      // 构建目标对象
      const yapiInterfaceItem: YapiIntefaceItem = {
        method,
        project_id: projectId,
        catid,
        title: summary || 'No summary',
        desc: description || 'No description',
        tag: [catName],
        path,
        req_body_type: 'json',
        res_body_type: 'json',
        res_body_is_json_schema: true,
        req_body_is_json_schema: true,
        res_body: JSON.stringify(resBody, null, 2),
        req_body_other: JSON.stringify(reqBody, null, 2),
        dataSync: 'good'
      };
      targetList.push(yapiInterfaceItem);
    });

    const url = `${target.yapi_host}/api/interface/save?token=${target.token}`;

    // 每次并发5个批量请求
    const batchSize = 5;
    try {
      for (let i = 0; i < targetList.length; i += batchSize) {
        const batch = targetList.slice(i, i + batchSize);
        await Promise.all(batch.map(item => axios.post(url, item)));
      }
    } catch (error) {
      logger.error(`导入yapi接口失败: ${error}`);
      return false;
    }
    return true;
  }
}
