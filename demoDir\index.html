<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>数独展示</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    table {
      border-collapse: collapse;
      margin: 0 auto;
    }
    td {
      width: 40px;
      height: 40px;
      text-align: center;
      vertical-align: middle;
      border: 1px solid #333;
      font-size: 20px;
    }
    /* 加粗3x3宫格边框 */
    td:nth-child(3n) {
      border-right: 2px solid #000;
    }
    tr:nth-child(3n) td {
      border-bottom: 2px solid #000;
    }
    table, tr:first-child td {
      border-top: 2px solid #000;
    }
    table, tr td:first-child {
      border-left: 2px solid #000;
    }

    /* 九个大方格背景色交替 */
    tbody tr:nth-child(3n-2) td:nth-child(3n-2),
    tbody tr:nth-child(3n-2) td:nth-child(3n-1),
    tbody tr:nth-child(3n-2) td:nth-child(3n),
    tbody tr:nth-child(3n-1) td:nth-child(3n-2),
    tbody tr:nth-child(3n-1) td:nth-child(3n-1),
    tbody tr:nth-child(3n-1) td:nth-child(3n),
    tbody tr:nth-child(3n) td:nth-child(3n-2),
    tbody tr:nth-child(3n) td:nth-child(3n-1),
    tbody tr:nth-child(3n) td:nth-child(3n) {
      background-color: #f3e6ff;
    }
    tbody tr:nth-child(3n-2) td:nth-child(3n+1),
    tbody tr:nth-child(3n-2) td:nth-child(3n+2),
    tbody tr:nth-child(3n-2) td:nth-child(3n+3),
    tbody tr:nth-child(3n-1) td:nth-child(3n+1),
    tbody tr:nth-child(3n-1) td:nth-child(3n+2),
    tbody tr:nth-child(3n-1) td:nth-child(3n+3),
    tbody tr:nth-child(3n) td:nth-child(3n+1),
    tbody tr:nth-child(3n) td:nth-child(3n+2),
    tbody tr:nth-child(3n) td:nth-child(3n+3) {
      background-color: #e6ccff;
    }

    /* 九宫格背景色交替，9个大方格中交替不同颜色 */
    tbody tr:nth-child(3n-2) td:nth-child(3n-2),
    tbody tr:nth-child(3n-1) td:nth-child(3n-1),
    tbody tr:nth-child(3n) td:nth-child(3n) {
      background-color: #d1b3ff;
    }
    tbody tr:nth-child(3n-2) td:nth-child(3n),
    tbody tr:nth-child(3n-1) td:nth-child(3n-2),
    tbody tr:nth-child(3n) td:nth-child(3n-1) {
      background-color: #b380ff;
    }
  </style>
</head>
<body>
  <h1>数独示例</h1>
  <table>
    <tbody>
      <tr>
        <td>5</td><td>3</td><td></td><td></td><td>7</td><td></td><td></td><td></td><td></td>
      </tr>
      <tr>
        <td>6</td><td></td><td></td><td>1</td><td>9</td><td>5</td><td></td><td></td><td></td>
      </tr>
      <tr>
        <td></td><td>9</td><td>8</td><td></td><td></td><td></td><td></td><td>6</td><td></td>
      </tr>
      <tr>
        <td>8</td><td></td><td></td><td></td><td>6</td><td></td><td></td><td></td><td>3</td>
      </tr>
      <tr>
        <td>4</td><td></td><td></td><td>8</td><td></td><td>3</td><td></td><td></td><td>1</td>
      </tr>
      <tr>
        <td>7</td><td></td><td></td><td></td><td>2</td><td></td><td></td><td></td><td>6</td>
      </tr>
      <tr>
        <td></td><td>6</td><td></td><td></td><td></td><td></td><td>2</td><td>8</td><td></td>
      </tr>
      <tr>
        <td></td><td></td><td></td><td>4</td><td>1</td><td>9</td><td></td><td></td><td>5</td>
      </tr>
      <tr>
        <td></td><td></td><td></td><td></td><td>8</td><td></td><td></td><td>7</td><td>9</td>
      </tr>
    </tbody>
  </table>
</body>
</html>
