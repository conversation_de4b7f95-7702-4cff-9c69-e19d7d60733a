// 获取并检查配置参数
import { exec } from 'child_process';
import minimist from "minimist";
import axios from 'axios';
import crypto from 'crypto';
import * as fs from "fs";
import { v4 as uuidv4 } from 'uuid';
import { logger } from "../logger";
import { ProjectConfig } from "../types";
import { pbToYapiPrompt } from "./const";


const taijiUrl = "http://taiji-stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions"
const model = "DeepSeek-V3-Online-64K"

// const model = "Hunyuan-TurboS-32k"


function signRequest(data: string) {
  const hmac = crypto.createHmac('sha256', 'eYh8@fKx2&zQm9#sPw3*rLn6');
  hmac.update(data, 'utf-8');
  return hmac.digest('base64');
}

export function getConfig() {
  let projects: ProjectConfig[] = [];
  let valid = true;

  const argv = minimist(process.argv.slice(2) || '');
  logger.info(`启动参数: ${JSON.stringify(argv)}`);

  if (argv.config) {
    try {
      const config = JSON.parse(argv.config);
      projects = config.projects || [];
    } catch (error) {
      logger.error('解析配置失败', error);
      valid = false;
    }
  }

  const configFile = process.env.config_file || argv.config_file;
  logger.info(`configFile: ${configFile}`);
  if (configFile) {
    try {
      const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
      projects = config.projects || [];
    } catch (error) {
      logger.error('读取配置文件失败', error);
      valid = false;
    }
  }

  return {valid, projects};
}

// 将pb文件内容转为yapi接口json参数
export async function pbToYapiJsonHy(functionName: string, pb: string) {
  const params = {
    query_id: "test_query_id_" + uuidv4(),
    model,
    messages: [
      {role: "system", content: pbToYapiPrompt},
      {
        role: "user", content: `pb内容如下：${pb}\n
生成${functionName}接口yapi的请求和响应参数`
      },
    ],
    response_format: {"type": "json_object"},
    stream: false,
  };
  const response = await axios.post(taijiUrl, params, {
    headers: {
      "Content-Type": "application/json",
      Authorization: "40cb7683-a8b8-4aa0-a763-2dcbeb9fc121"
    },
  });
  const res = response.data.choices[0].message.content;
  return getJsonData(res);
}

// 将pb文件内容转为yapi接口json参数
export async function pbToYapiJsonAzure(functionName: string, pb: string) {
  try {
    const params = {
      model: "gpt-4.1-nano",  // 响应速度较快
      messages: [
        {role: "system", content: pbToYapiPrompt},
        {
          role: "user", content: `pb内容如下：${pb}\n
生成${functionName}接口yapi的请求和响应参数，注意注释description需要严格根据proto文件中的定义来填写`
        },
      ],
    }
    const dataString = JSON.stringify(params, null); // 保证没有多余空格和缩进
    const signature = signRequest(dataString);

    const promptUrl = 'https://aigc.levelinfiniteapps.com/api/svr/custom/prompt'
    const res = await axios.post(promptUrl, params, {
      headers: {'X-Signature': signature, 'Content-Type': 'application/json'},
    });

    return res.data.data;
  } catch (error) {
    console.error('Error translating text:', error);
  }
}

async function getJsonData(content: string) {
  // 转成发json，且兼容字符串```json开头和```结尾的形式
  const jsonStr = content.replace(/^```json/g, '').replace(/```$/g, '');
  try {
    return JSON.parse(jsonStr);
  } catch (error) {
    logger.warn(`Error parsing json: ${jsonStr}`);
    return undefined;
  }
}

// 获取绝对路径，处理磁盘前面有/的情况
export function getAbsolutePath(originPath: string) {
  const driveLetterMatch = originPath.match(/^\/([a-zA-Z]:\/)/);
  if (driveLetterMatch) {
    return driveLetterMatch[1] + originPath.slice(driveLetterMatch[0].length);
  }
  return originPath;
}

/**
 * 检查系统中是否安装了swag命令
 * @returns Promise<boolean> 如果安装了返回true，否则返回false
 */
export function isSwagInstalled(): Promise<boolean> {
  return new Promise((resolve) => {
    // 尝试执行 swag -v 命令来检查是否安装
    exec('swag -v', (error, stdout, stderr) => {
      if (error) {
        // 命令执行失败，说明没有安装swag或不在PATH中
        logger.warn('Swag command is not installed or not in PATH');
        resolve(false);
      } else if (stderr) {
        // 有错误输出，可能是命令存在但有问题
        logger.warn(`Swag command check returned error: ${stderr}`);
        resolve(false);
      } else {
        // 命令执行成功，说明已安装swag
        logger.info(`Swag is installed: ${stdout.trim()}`);
        resolve(true);
      }
    });
  });
}

/**
 * 在指定目录执行swag init命令生成swagger文档
 * @param projectPath 项目路径，用于执行swag init命令的目录
 * @returns Promise<{success: boolean, message: string, swaggerPath?: string}>
 */
export function executeSwagInit(projectPath: string): Promise<{success: boolean, message: string, swaggerPath?: string}> {
  return new Promise((resolve) => {
    const absolutePath = getAbsolutePath(projectPath);
    logger.info(`执行swag init命令，项目路径: ${absolutePath}`);

    // 执行 swag init 命令
    exec('swag init', { cwd: absolutePath }, (error, stdout, stderr) => {
      if (error) {
        logger.error(`swag init执行失败: ${error.message}`);
        resolve({
          success: false,
          message: `swag init执行失败: ${error.message}`
        });
      } else {
        const swaggerPath = `${absolutePath}/docs/swagger.json`;
        resolve({
          success: true,
          message: `swag init执行成功: ${stdout}`,
          swaggerPath
        });
      }
    });
  });
}
