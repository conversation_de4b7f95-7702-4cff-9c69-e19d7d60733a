import { logger } from '../logger';

interface SchemaNode {
  type: string;
  description?: string;
  properties?: Record<string, SchemaNode>;
  items?: SchemaNode;
  required?: string[];
  [key: string]: any;
}

/**
 * Enhances JSON schema with comments from Proto definitions
 * @param jsonSchema The JSON schema to enhance
 * @param protoContent The Proto content with comments
 * @returns Enhanced JSON schema with comments from Proto
 */
export function enhanceJsonSchema(jsonSchema: any, protoContent: string): any {
  // Create a map of field names to comments from the proto content
  const commentMap = extractCommentsFromProto(protoContent);
  
  // Deep clone the input schema to avoid modifying the original
  const enhancedSchema = JSON.parse(JSON.stringify(jsonSchema));
  
  // Process the schema recursively
  processSchemaNode(enhancedSchema, commentMap);
  
  return enhancedSchema;
}

/**
 * Extracts comments from Proto content and creates a map of field names to comments
 */
function extractCommentsFromProto(protoContent: string): Map<string, string> {
  const commentMap = new Map<string, string>();
  
  // Split the proto content into lines
  const lines = protoContent.split('\n');
  
  // Regular expression to match field definitions with comments
  const fieldRegex = /\s*(\w+)\s+(\w+)\s*=\s*\d+.*?\/\/\s*(.*)/;
  
  for (const line of lines) {
    const match = line.match(fieldRegex);
    if (match) {
      const [, , fieldName, comment] = match;
      commentMap.set(fieldName, comment.trim());
    }
  }
  
  return commentMap;
}

/**
 * Recursively processes a schema node to add descriptions from the comment map
 */
function processSchemaNode(node: SchemaNode, commentMap: Map<string, string>): void {
  // If this is an object with properties, process each property
  if (node.properties) {
    for (const [propName, propSchema] of Object.entries(node.properties)) {
      // If the property doesn't have a description but we have a comment for it, add it
      if (!propSchema.description && commentMap.has(propName)) {
        propSchema.description = commentMap.get(propName);
      }
      
      // Recursively process this property
      processSchemaNode(propSchema, commentMap);
    }
  }
  
  // If this is an array, process its items schema
  if (node.items) {
    processSchemaNode(node.items, commentMap);
  }
}

/**
 * Main function to enhance a JSON schema with Proto comments
 * @param jsonSchemaStr JSON schema string
 * @param protoContent Proto content with comments
 * @returns Enhanced JSON schema string
 */
export function enhanceJsonSchemaWithComments(jsonSchemaStr: string, protoContent: string): string {
  try {
    const jsonSchema = JSON.parse(jsonSchemaStr);
    const enhancedSchema = enhanceJsonSchema(jsonSchema, protoContent);
    return JSON.stringify(enhancedSchema);
  } catch (error) {
    logger.error(`Error enhancing JSON schema: ${error}`);
    return jsonSchemaStr; // Return original on error
  }
}

