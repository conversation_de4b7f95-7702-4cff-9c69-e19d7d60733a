const Koa = require('koa');
const Router = require('koa-router');
const bodyParser = require('koa-bodyparser');
const axios = require('axios');
const os = require('os');
const logger = require('./logger');
const dayjs = require("dayjs");
const { pbParse } = require('./controllers/mcp');

const app = new Koa();
const router = new Router();
const platform = os.platform();

// 错误处理中间件
app.use(async (ctx, next) => {
  try {
    await next();
    // 记录成功的请求
    logger.info('Request completed', {
      method: ctx.method, path: ctx.path, status: ctx.status, duration: ctx.response.get('X-Response-Time')
    });
  } catch (err) {
    ctx.status = err.status || 500;
    ctx.body = {
      error: err.message
    };
    // 记录错误
    logger.error('Request error', {
      error: err.message, stack: err.stack, method: ctx.method, path: ctx.path, status: ctx.status
    });
  }
});

// 响应时间中间件
app.use(async (ctx, next) => {
  const start = Date.now();
  await next();
  const ms = Date.now() - start;
  ctx.set('X-Response-Time', `${ms}ms`);
});

app.use(bodyParser());

router.get('/', async (ctx) => {
  logger.info('Handling root request');
  ctx.body = 'Hello Huanyuan Proxy!';
});

router.post('/mcp/pb_parse', async (ctx) => {
  logger.info('Handling chat completions request');
  const data = await pbParse(ctx);
  ctx.body = data;
});


// 实现/openapi/chat/completions接口
router.all('/openapi/chat/completions', async (ctx) => {
  try {
    // 修改请求体中的messages内容
    if (ctx.method === 'POST' && ctx.request.body && ctx.request.body.messages) {
      ctx.request.body.messages = ctx.request.body.messages.map(msg => {
        if (msg.content && typeof msg.content === 'object' && !Array.isArray(msg.content) && msg.content.text) {
          return {
            ...msg,
            content: msg.content.text
          };
        } else if (msg.content && Array.isArray(msg.content)) {
          // 处理content为数组的情况
          const textContents = msg.content
            .filter(item => item.type === 'text' && item.text)
            .map(item => item.text);
          return {
            ...msg,
            content: textContents.join('\n\n')
          };
        }
        return msg;
      });
    }

    // 透传请求到目标服务器
    logger.info('请求转发body:', ctx.request.body);

    const hyUrl = platform === 'linux' ? 'http://taiji-stream-server-online-openapi.turbotke.production.polaris:1081'
      : 'http://taiji-stream-server-online-openapi.turbotke.production.polaris:8080'

    try {
      const response = await axios({
        method: ctx.method,
        url: `${hyUrl}/openapi/chat/completions`,
        headers: {
          "Content-Type": "application/json",
          "Authorization": ctx.headers.authorization,
        },
        data: ctx.request.body,
        responseType: ctx.request.body.stream ? 'stream' : 'json'
      });

      // 设置响应头
      ctx.status = response.status;
      Object.keys(response.headers).forEach(key => {
        ctx.set(key, response.headers[key]);
      });

      ctx.body = response.data;  // 透传响应数据
    } catch (error) {
      logger.info('请求混元接口失败:', error);
      // 将请求参数写json文件中
      const fs = require('fs');
      const errJsonName = `logs/error_body_${dayjs().format('YYYY-MM-DD_HH:mm:ss')}}.json`;
      fs.writeFileSync(errJsonName, JSON.stringify(ctx.request.body));

      ctx.status = error.response?.status || 500;
      ctx.body = {
        error: `${error.message},${error.stack}`
      };
    }
  } catch (error) {
    logger.info('请求转发失败:', error);
    ctx.status = 500;
    ctx.body = {
      error: `${error.message},${error.stack}`
    };
  }
});

app.use(router.routes()).use(router.allowedMethods());

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception', {
    error: err.message,
    stack: err.stack
  });
  // 给进程一些时间来处理剩余的日志写入
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', {
    reason: reason,
    promise: promise
  });
});

const PORT = 9200;
app.listen(PORT, () => {
  logger.info(`Server started on port ${PORT}`);
});
