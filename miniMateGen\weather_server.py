import os
import asyncio
import httpx
import uuid
import hmac
import base64
import hashlib
from typing import Any
from datetime import datetime
from urllib.parse import urlencode
from mcp.server.fastmcp import FastMCP
from dotenv import load_dotenv

load_dotenv()  # 加载 .env 文件，确保 API Key 受到保护

secretId = os.getenv("tc_weather_secret_id")
secretKey = os.getenv("tc_weather_secret_key")

# 初始化MCP服务器
mcp = FastMCP("WeatherServer")


def get_sign():
  dt = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
  sign_str = f"x-date: {dt}"
  sign = base64.b64encode(hmac.new(secretKey.encode('utf-8'), sign_str.encode('utf-8'), hashlib.sha1).digest())
  auth = f'{{"id": "{secretId}", "x-date": "{dt}" , "signature": "{sign.decode("utf-8")}"}}'
  return auth


async def fetch_weather(city: str) -> dict[str, Any] | None:
  query_params = {
    "areaCn": city,
  }
  url = 'https://ap-guangzhou.cloudmarket-apigw.com/service-6drgk6su/lundear/weather1d'
  if query_params:
    url = url + '?' + urlencode(query_params)

  headers = {
    'request-id': str(uuid.uuid1()),
    'Authorization': get_sign()
  }

  async with httpx.AsyncClient() as client:
    try:
      response = await client.get(url, headers=headers)
      response.raise_for_status()
      return response.json()  # 返回字典类型
    except httpx.HTTPStatusError as e:
      return {"error": f"HTTP 错误: {e.response.status_code}"}
    except Exception as e:
      return {"error": f"请求失败: {str(e)}"}


def format_weather(res: dict) -> str:
  if res["code"] != '0':
    return f"❌ 天气查询失败: {res['desc']}"

  data = res["data"]
  city = data["cityInfo"]
  temp = data["now"]["temp"]
  humidity = data["now"]["sd"]
  wind_speed = data["now"]["wse"]
  description = data["now"]["weather"]
  return (
    f"🌍 {city['cityCn']}, {city['provCn']}, {city['nationCn']}\n"
    f"🌡 温度: {temp}°C\n"
    f"💧 湿度: {humidity}\n"
    f"🌬 风速: {wind_speed}\n"
    f"🌤 天气: {description}\n"
  )


@mcp.tool()
async def query_weather(city: str) -> str:
  """
  输入指定城市的英文名称，返回今日天气查询结果。
  :param city: 城市名称（需使用中文，如深圳，北京）
  :return: 格式化后的天气信息
  """
  data = await fetch_weather(city)
  return format_weather(data)


if __name__ == "__main__":
  # 以标准 I/O 方式运行 MCP 服务器
  mcp.run(transport='stdio')


# data = asyncio.run(
#   query_weather("北京")
# )
# print(data)
