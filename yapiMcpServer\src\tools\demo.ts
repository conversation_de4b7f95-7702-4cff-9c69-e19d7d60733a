import * as fs from 'fs';
import * as path from 'path';

interface StructInfo {
  name: string;
  definition: string;
  dependencies: string[];
}

/**
 * Finds a Go struct definition and its dependencies in a project
 * @param structName The name of the struct to find (e.g., "getSummaryV2Rsp")
 * @param rootDir The root directory of the project (e.g., "streamer_backend")
 * @returns A string containing the struct definition and all its dependencies
 */
export function findStructDefinition(structName: string, rootDir: string): string {
  const structInfoMap: Map<string, StructInfo> = new Map();
  const pendingStructs: string[] = [structName];
  const processedStructs: Set<string> = new Set();

  // Process structs until no more pending structs
  while (pendingStructs.length > 0) {
    const currentStruct = pendingStructs.shift()!;
    if (processedStructs.has(currentStruct)) continue;
    
    const structInfo = findStruct(currentStruct, rootDir);
    if (structInfo) {
      structInfoMap.set(currentStruct, structInfo);
      processedStructs.add(currentStruct);
      
      // Add dependencies to pending list
      for (const dep of structInfo.dependencies) {
        if (!processedStructs.has(dep)) {
          pendingStructs.push(dep);
        }
      }
    }
  }

  // Build the result string with all struct definitions
  return buildResultString(structInfoMap);
}

/**
 * Finds a struct definition in the project
 */
function findStruct(structName: string, rootDir: string): StructInfo | null {
  const files = getAllGoFiles(rootDir);
  
  for (const file of files) {
    const content = fs.readFileSync(file, 'utf8');
    const structMatch = findStructInContent(content, structName);
    
    if (structMatch) {
      const dependencies = extractDependencies(structMatch.definition, content);
      return {
        name: structName,
        definition: structMatch.definition,
        dependencies
      };
    }
  }
  
  return null;
}

/**
 * Gets all Go files in the project
 */
function getAllGoFiles(dir: string): string[] {
  let results: string[] = [];
  const list = fs.readdirSync(dir);
  
  for (const file of list) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      results = results.concat(getAllGoFiles(filePath));
    } else if (path.extname(file) === '.go') {
      results.push(filePath);
    }
  }
  
  return results;
}

/**
 * Finds a struct definition in file content
 */
function findStructInContent(content: string, structName: string): { definition: string } | null {
  // 改进匹配，支持换行和多空格，匹配 type StructName struct { ... }
  const structRegex = new RegExp(`type\\s+${structName}\\s+struct\\s*{`, 'g');
  const matches: RegExpExecArray[] = [];
  let match: RegExpExecArray | null;
  while ((match = structRegex.exec(content)) !== null) {
    matches.push(match);
  }
  if (matches.length === 0) return null;

  // 取第一个匹配位置
  const startIdx = matches[0].index;

  // 从 { 开始计数大括号，找到对应的结束 }
  let braceCount = 0;
  let inStruct = false;
  let endIdx = -1;
  for (let i = startIdx; i < content.length; i++) {
    if (content[i] === '{') {
      braceCount++;
      inStruct = true;
    } else if (content[i] === '}') {
      braceCount--;
      if (inStruct && braceCount === 0) {
        endIdx = i;
        break;
      }
    }
  }
  if (endIdx === -1) return null;

  // 取结构体定义字符串
  const structDef = content.slice(startIdx, endIdx + 1);
  return { definition: structDef };
}

/**
 * Extracts dependencies from a struct definition
 */
function extractDependencies(definition: string, fileContent: string): string[] {
  const dependencies: string[] = [];
  
  // Match field types that are custom structs (not built-in types)
  const fieldTypeRegex = /\s+(\w+)(\s+|\*)\s*(\w+)\s+`/g;
  let match;
  
  while ((match = fieldTypeRegex.exec(definition)) !== null) {
    const typeName = match[3];
    // Filter out built-in types
    if (!isBuiltInType(typeName)) {
      dependencies.push(typeName);
    }
  }
  
  // Also match slice/array types
  const sliceTypeRegex = /\[\](\*?)(\w+)/g;
  while ((match = sliceTypeRegex.exec(definition)) !== null) {
    const typeName = match[2];
    if (!isBuiltInType(typeName)) {
      dependencies.push(typeName);
    }
  }
  
  // Handle embedded structs (structs without field names)
  const embeddedStructRegex = /\s+(\w+)\s*$/gm;
  while ((match = embeddedStructRegex.exec(definition)) !== null) {
    const typeName = match[1];
    if (!isBuiltInType(typeName)) {
      dependencies.push(typeName);
    }
  }
  
  return dependencies;
}

/**
 * Checks if a type is a built-in Go type
 */
function isBuiltInType(typeName: string): boolean {
  const builtInTypes = [
    'string', 'int', 'int8', 'int16', 'int32', 'int64',
    'uint', 'uint8', 'uint16', 'uint32', 'uint64',
    'float32', 'float64', 'bool', 'byte', 'rune',
    'complex64', 'complex128', 'error', 'interface'
  ];
  
  return builtInTypes.includes(typeName.toLowerCase());
}

/**
 * Builds the result string from the struct info map
 */
function buildResultString(structInfoMap: Map<string, StructInfo>): string {
  let result = '';
  
  // Add each struct definition to the result
  for (const [_, structInfo] of structInfoMap) {
    result += structInfo.definition + '\n\n';
  }
  
  return result.trim();
}


// Example usage:
const result = findStructDefinition("getRankListAuthorDetailReq", "/project/trpc-project/streamer_backend");
console.log(result);
