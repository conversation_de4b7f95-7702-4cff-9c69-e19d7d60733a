import os
import sys
import asyncio
import json
from typing import Optional
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from dotenv import load_dotenv
from contextlib import AsyncExitStack
from openai import AzureOpenAI

load_dotenv()   # 加载 .env 文件，确保 API Key 受到保护

class MCPClient:
  def __init__(self):
    self.model = "gpt-4o-mini"
    """初始化 MCP 客户端"""
    self.exit_stack = AsyncExitStack()
    self.api_version = os.getenv("api_version")  # 读取 OpenAI API Key
    self.azure_endpoint = os.getenv("azure_endpoint")  # 读取 BASE YRL
    self.api_key = os.getenv("api_key")  # 读取 model

    self.client = AzureOpenAI(
      api_key=self.api_key,
      api_version=self.api_version,
      azure_endpoint=self.azure_endpoint
    )
    self.session = Optional[ClientSession] | None


  async def process_query(self, query: str) -> str:
    """
    使用大模型处理查询并调用可用的 MCP 工具 (Function Calling)
    """
    messages = [{"role": "user", "content": query}]

    response = await self.session.list_tools()

    available_tools = [{
      "type": "function",
      "function": {
        "name": tool.name,
        "description": tool.description,
        "parameters": tool.inputSchema,
      }
    } for tool in response.tools]

    try:
      response = await asyncio.get_event_loop().run_in_executor(
        None,
        lambda: self.client.chat.completions.create(
          model=self.model,
          messages=messages,
          tools=available_tools
        )
      )
      # 处理返回的内容
      content = response.choices[0]
      if content.finish_reason == "tool_calls":
        # 如何是需要使用工具，就解析工具
        tool_call = content.message.tool_calls[0]
        tool_name = tool_call.function.name
        tool_args = json.loads(tool_call.function.arguments)

        print(f"\n[Calling tool '{tool_name}' with args {tool_args}]\n")

        # 执行工具
        result = await self.session.call_tool(tool_name, tool_args)

        # 将模型返回的调用哪个工具数据和工具执行完成后的数据都存入messages中
        messages.append(content.message.model_dump())

        messages.append({
          "role": "tool",
          "content": result.content[0].text,
          "tool_call_id": tool_call.id,
        })

        # 将上面的结果再返回给大模型用于生产最终的结果
        response = self.client.chat.completions.create(
          model=self.model,
          messages=messages,
        )
        return response.choices[0].message.content

      return content.message.content
    except Exception as e:
      return f"⚠️ 调用 OpenAI API 时出错: {str(e)}"

  async def connect_to_mock_server(self, server_script_path: str):
    """模拟 MCP 服务器的连接（暂不连接真实服务器）"""
    is_python = server_script_path.endswith('.py')
    is_js = server_script_path.endswith('.js')
    if not (is_python or is_js):
      raise ValueError("服务器脚本必须是 .py 或 .js 文件")

    command = "python" if is_python else "node"
    server_params = StdioServerParameters(
      command=command,
      args=[server_script_path],  # server.py
      env=None
    )

    # 启动 MCP 服务器并建立通信
    stdio_transport = await self.exit_stack.enter_async_context(
      stdio_client(server_params)
    )
    self.stdio, self.write = stdio_transport  # 拿到读写流
    self.session = await self.exit_stack.enter_async_context(
      ClientSession(self.stdio, self.write)  # 创建 MCP 客户端会话，与服务器交互
    )

    await self.session.initialize()  # 发送初始化消息给服务器，等待服务器就绪

    # 列出 MCP 服务器上的工具，向 MCP 服务器请求所有已注册的工具（用 @mcp.tool() 标记）。
    response = await self.session.list_tools()
    tools = response.tools
    print("\n已连接到服务器，支持以下工具:", [tool.name for tool in tools])

  async def chat_loop(self):
    """运行交互式聊天循环"""
    print("\nMCP 客户端已启动！输入 'quit' 退出")

    while True:
      try:
        query = input("\nQuery: ").strip()
        if query.lower() == 'quit':
          break

        response = await self.process_query(query)  # 发送用户输入到 OpenAI API
        print(f"\n🤖 OpenAI: {response}")
      except Exception as e:
        print(f"\n⚠️ 发生错误: {str(e)}")

  async def cleanup(self):
    """清理资源"""
    await self.exit_stack.aclose()


async def main():
  if len(sys.argv) < 2:
    print("Usage: python client.py <path_to_server_script>")
    sys.exit(1)

  client = MCPClient()
  try:
    # ['client.py', 'weather_server.py']
    await client.connect_to_mock_server(sys.argv[1])
    await client.chat_loop()
  finally:
    await client.cleanup()

# uv run client.py weather_server.py
if __name__ == "__main__":
  asyncio.run(main())
