import { enhanceJsonSchema, enhanceJsonSchemaWithComments } from './enhanceJsonSchema';

// Test data
const sampleProtoContent = `
syntax = "proto3";

message GetDataReq {
  int64 id = 1;                 // 资源ID
  string user_name = 2;         // 用户名称
  TypeFilter filter = 3;        // 过滤类型
  int32 page = 4;               // 页码
  int32 page_size = 5;          // 每页数量
  repeated string tags = 6;     // 标签列表
  map<string, string> extras = 7; // 扩展数据
}

message GetDataRsp {
  int32 code = 1;               // 响应码
  string message = 2;           // 响应消息
  DataInfo data_info = 3;       // 数据信息
}
`;

const sampleJsonSchema = {
  type: "object",
  properties: {
    id: {
      type: "integer"
    },
    userName: {
      type: "string"
    },
    user_name: {
      type: "string"
    },
    filter: {
      type: "object"
    },
    page: {
      type: "integer"
    },
    pageSize: {
      type: "integer"
    },
    page_size: {
      type: "integer"
    },
    tags: {
      type: "array",
      items: {
        type: "string"
      }
    },
    extras: {
      type: "object"
    }
  }
};

// Test function
function testEnhancement() {
  console.log('Testing JSON Schema Enhancement...\n');

  console.log('=== Testing Field Matching ===');
  const enhanced = enhanceJsonSchema(sampleJsonSchema, sampleProtoContent);

  // Check specific field enhancements
  console.log('id description:', enhanced.properties.id.description);
  console.log('userName description:', enhanced.properties.userName.description);
  console.log('user_name description:', enhanced.properties.user_name.description);
  console.log('page description:', enhanced.properties.page.description);
  console.log('pageSize description:', enhanced.properties.pageSize.description);
  console.log('page_size description:', enhanced.properties.page_size.description);
  console.log('tags description:', enhanced.properties.tags.description);
  console.log('extras description:', enhanced.properties.extras.description);

  console.log('\n=== Summary ===');
  let enhancedCount = 0;
  for (const [key, prop] of Object.entries(enhanced.properties)) {
    if ((prop as any).description) {
      enhancedCount++;
      console.log(`✓ ${key}: ${(prop as any).description}`);
    } else {
      console.log(`✗ ${key}: No description added`);
    }
  }

  console.log(`\nTotal enhanced fields: ${enhancedCount}/${Object.keys(enhanced.properties).length}`);
}

// Run test if this file is executed directly
if (require.main === module) {
  testEnhancement();
}

export { testEnhancement };
