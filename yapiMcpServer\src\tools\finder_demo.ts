import * as fs from 'fs';
import * as path from 'path';

interface TypeDefinition {
  name: string;
  definition: string;
  dependencies: string[]; // 依赖的类型名称
  filePath: string;       // 类型定义所在的文件路径
}

/**
 * 从Go文件中提取类型定义
 * @param filePath Go文件路径
 * @param content 文件内容
 * @returns 文件中的所有类型定义
 */
function extractTypesFromFile(filePath: string, content: string): Map<string, TypeDefinition> {
  const types = new Map<string, TypeDefinition>();
  
  // 匹配所有的类型定义
  const typeRegex = /type\s+([A-Za-z0-9_]+)\s+struct\s+{([^}]*)}/g;
  let match;
  
  while ((match = typeRegex.exec(content)) !== null) {
    const typeName = match[1];
    const typeBody = match[2];
    const definition = match[0];

    console.log('typeName', typeName);
    console.log('typeBody', typeBody);
    console.log('definition', definition);

    
    // 提取依赖类型
    const dependencies: string[] = [];
    
    // 匹配字段类型，改进正则表达式以更好地捕获数组和指针类型
    const fieldRegex = /\s*[A-Za-z0-9_]+\s+(?:\[[\]\*]*)?([A-Z][A-Za-z0-9_]*)(?:\])?/g;
    let fieldMatch;
    
    while ((fieldMatch = fieldRegex.exec(typeBody)) !== null) {
      const fieldType = fieldMatch[1];
      if (!dependencies.includes(fieldType) && !isBuiltinType(fieldType)) {
        dependencies.push(fieldType);
      }
    }
    
    // 匹配嵌入的结构体
    const embeddedRegex = /\s*([A-Z][A-Za-z0-9_]*)\s*$/gm;
    let embeddedMatch;
    
    while ((embeddedMatch = embeddedRegex.exec(typeBody)) !== null) {
      const embeddedType = embeddedMatch[1];
      if (!dependencies.includes(embeddedType) && !isBuiltinType(embeddedType)) {
        dependencies.push(embeddedType);
      }
    }
    
    types.set(typeName, {
      name: typeName,
      definition,
      dependencies,
      filePath
    });
  }
  
  return types;
}

/**
 * 检查是否是Go的内置类型
 */
function isBuiltinType(typeName: string): boolean {
  const builtinTypes = [
    'bool', 'byte', 'complex64', 'complex128', 'error', 'float32', 'float64',
    'int', 'int8', 'int16', 'int32', 'int64', 'rune', 'string',
    'uint', 'uint8', 'uint16', 'uint32', 'uint64', 'uintptr',
    'any', 'interface'
  ];
  
  return builtinTypes.includes(typeName);
}

/**
 * 在项目中查找所有类型定义
 * @param rootDir 项目根目录
 * @returns 所有类型定义的映射
 */
async function findAllTypesInProject(rootDir: string = 'streamer_backend'): Promise<Map<string, TypeDefinition>> {
  const allTypes = new Map<string, TypeDefinition>();
  
  // 递归遍历目录
  async function traverseDir(dir: string): Promise<void> {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        // 递归遍历子目录
        await traverseDir(filePath);
      } else if (stat.isFile() && file.endsWith('.go')) {
        // 读取Go文件内容
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 提取类型定义
        const types = extractTypesFromFile(filePath, content);
        
        // 合并到全局映射
        for (const [name, def] of types.entries()) {
          allTypes.set(name, def);
        }
      }
    }
  }
  
  await traverseDir(rootDir);
  return allTypes;
}

/**
 * 查找类型定义及其所有依赖
 * @param typeName 要查找的类型名称
 * @param rootDir 项目根目录
 * @returns 类型定义及其所有依赖
 */
async function findTypeWithDependencies(typeName: string, rootDir: string = 'streamer_backend'): Promise<TypeDefinition[]> {
  // 首先查找项目中的所有类型
  const allTypes = await findAllTypesInProject(rootDir);
  
  // 如果找不到指定类型，返回空数组
  if (!allTypes.has(typeName)) {
    console.log(`类型 ${typeName} 未在项目中找到`);
    return [];
  }
  
  const result: TypeDefinition[] = [];
  const processedTypes = new Set<string>();
  
  // 递归处理类型及其依赖
  function processType(name: string): void {
    if (processedTypes.has(name)) return;
    processedTypes.add(name);
    
    const typeDefinition = allTypes.get(name);
    if (typeDefinition) {
      result.push(typeDefinition);
      console.log(`处理类型: ${name}, 依赖: ${typeDefinition.dependencies.join(', ')}`);
      
      // 递归处理依赖
      for (const dependency of typeDefinition.dependencies) {
        if (allTypes.has(dependency)) {
          processType(dependency);
        } else {
          console.log(`警告: 依赖类型 ${dependency} 未找到`);
        }
      }
    }
  }
  
  processType(typeName);
  return result;
}

/**
 * 查找并打印类型定义及其所有依赖
 * @param typeName 要查找的类型名称
 * @param rootDir 项目根目录
 */
async function findAndPrintTypeDefinition(typeName: string, rootDir: string = 'streamer_backend'): Promise<void> {
  console.log(`正在查找类型 ${typeName} 及其依赖...`);
  
  const typeDefinitions = await findTypeWithDependencies(typeName, rootDir);
  
  if (typeDefinitions.length === 0) {
    console.log(`类型 ${typeName} 未找到`);
    return;
  }
  
  // 打印主类型定义
  const mainType = typeDefinitions.find(t => t.name === typeName);
  if (mainType) {
    console.log(`// ${mainType.name} 定义 (在文件 ${mainType.filePath}):`);
    console.log(mainType.definition);
    console.log();
  }
  
  // 打印依赖类型定义
  const dependencies = typeDefinitions.filter(t => t.name !== typeName);
  if (dependencies.length > 0) {
    console.log(`// ${typeName} 依赖的类型定义:`);
    for (const dependency of dependencies) {
      console.log(`// ${dependency.name} 定义 (在文件 ${dependency.filePath}):`);
      console.log(dependency.definition);
      console.log();
    }
  }
}

// 使用示例
findAndPrintTypeDefinition("getRankListAuthorWorkListRsp", "/project/trpc-project/streamer_backend");
